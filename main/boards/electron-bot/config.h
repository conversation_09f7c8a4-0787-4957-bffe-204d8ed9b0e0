#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>

#define Right_Pitch_Pin GPIO_NUM_5  // 旋转
#define Right_Roll_Pin GPIO_NUM_4   // 推杆
#define Left_Pitch_Pin GPIO_NUM_7
#define Left_Roll_Pin GPIO_NUM_15
#define Body_Pin GPIO_NUM_6
#define Head_Pin GPIO_NUM_16

#define POWER_CHARGE_DETECT_PIN GPIO_NUM_14
#define POWER_ADC_UNIT ADC_UNIT_1
#define POWER_ADC_CHANNEL ADC_CHANNEL_2

#define AUDIO_INPUT_SAMPLE_RATE 16000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000
#define AUDIO_I2S_METHOD_SIMPLEX

#define AUDIO_I2S_MIC_GPIO_WS GPIO_NUM_40
#define AUDIO_I2S_MIC_GPIO_SCK GPIO_NUM_42
#define AUDIO_I2S_MIC_GPIO_DIN GPIO_NUM_41
#define AUDIO_I2S_SPK_GPIO_DOUT GPIO_NUM_17
#define AUDIO_I2S_SPK_GPIO_BCLK GPIO_NUM_18
#define AUDIO_I2S_SPK_GPIO_LRCK GPIO_NUM_8

#define DISPLAY_WIDTH 240
#define DISPLAY_HEIGHT 240
#define DISPLAY_MIRROR_X false
#define DISPLAY_MIRROR_Y true
#define DISPLAY_SWAP_XY false

#define DISPLAY_OFFSET_X 0
#define DISPLAY_OFFSET_Y 0

#define DISPLAY_BACKLIGHT_PIN GPIO_NUM_46
#define DISPLAY_BACKLIGHT_OUTPUT_INVERT false

#define DISPLAY_SPI_SCLK_PIN GPIO_NUM_11
#define DISPLAY_SPI_MOSI_PIN GPIO_NUM_10
#define DISPLAY_SPI_CS_PIN GPIO_NUM_12
#define DISPLAY_SPI_DC_PIN GPIO_NUM_13
#define DISPLAY_SPI_RESET_PIN GPIO_NUM_9

#define DISPLAY_SPI_SCLK_HZ (40 * 1000 * 1000)

#define BOOT_BUTTON_GPIO GPIO_NUM_0

#define ELECTRON_BOT_VERSION "1.1.3"
#endif  // _BOARD_CONFIG_H_
