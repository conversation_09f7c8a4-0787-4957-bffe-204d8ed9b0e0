
#pragma once

//关于开发板和屏幕的资料参考
//https://docs.espressif.com/projects/esp-dev-kits/en/latest/esp32s3/esp32-s3-lcd-ev-board/user_guide.html

#define GC9503V_LCD_H_RES 480
#define GC9503V_LCD_V_RES 480


#define GC9503V_LCD_LVGL_DIRECT_MODE            (1)
#define GC9503V_LCD_LVGL_AVOID_TEAR             (1)
#define GC9503V_LCD_RGB_BOUNCE_BUFFER_MODE      (1)
#define GC9503V_LCD_DRAW_BUFF_DOUBLE            (0)
#define GC9503V_LCD_DRAW_BUFF_HEIGHT            (100)
#define GC9503V_LCD_RGB_BUFFER_NUMS             (2)
#define GC9503V_LCD_RGB_BOUNCE_BUFFER_HEIGHT    (10)

#define GC9503V_LCD_PIXEL_CLOCK_HZ (16 * 1000 * 1000)
#define GC9503V_LCD_BK_LIGHT_ON_LEVEL 1
#define GC9503V_LCD_BK_LIGHT_OFF_LEVEL !GC9503V_LCD_BK_LIGHT_ON_LEVEL
#define GC9503V_PIN_NUM_BK_LIGHT GPIO_NUM_NC //GPIO_NUM_4
#define GC9503V_PIN_NUM_HSYNC GPIO_NUM_46
#define GC9503V_PIN_NUM_VSYNC GPIO_NUM_3
#define GC9503V_PIN_NUM_DE GPIO_NUM_17
#define GC9503V_PIN_NUM_PCLK GPIO_NUM_9

#define GC9503V_PIN_NUM_DATA0 GPIO_NUM_10 // B0
#define GC9503V_PIN_NUM_DATA1 GPIO_NUM_11 // B1
#define GC9503V_PIN_NUM_DATA2 GPIO_NUM_12 // B2
#define GC9503V_PIN_NUM_DATA3 GPIO_NUM_13 // B3
#define GC9503V_PIN_NUM_DATA4 GPIO_NUM_14 // B4

#define GC9503V_PIN_NUM_DATA5 GPIO_NUM_21  // G0

//如果开发板是V1.4 IO 定义为
#ifdef CONFIG_ESP_S3_LCD_EV_Board_1p4
    #define GC9503V_PIN_NUM_DATA6 GPIO_NUM_47  // G1
    #define GC9503V_PIN_NUM_DATA7 GPIO_NUM_48  // G2
#endif
//如果开发板是V1.5 IO 定义为
#ifdef CONFIG_ESP_S3_LCD_EV_Board_1p5
    #define GC9503V_PIN_NUM_DATA6  GPIO_NUM_8  // G1
    #define GC9503V_PIN_NUM_DATA7  GPIO_NUM_18  // G2
#endif


#define GC9503V_PIN_NUM_DATA8 GPIO_NUM_45  // G3
#define GC9503V_PIN_NUM_DATA9 GPIO_NUM_38   // G4
#define GC9503V_PIN_NUM_DATA10 GPIO_NUM_39 // G5

#define GC9503V_PIN_NUM_DATA11 GPIO_NUM_40 // R0
#define GC9503V_PIN_NUM_DATA12 GPIO_NUM_41  // R1
#define GC9503V_PIN_NUM_DATA13 GPIO_NUM_42 // R2
#define GC9503V_PIN_NUM_DATA14 GPIO_NUM_2 // R3
#define GC9503V_PIN_NUM_DATA15 GPIO_NUM_1 // R4

#define GC9503V_PIN_NUM_DISP_EN -1

#define GC9503V_LCD_IO_SPI_CS_1 (IO_EXPANDER_PIN_NUM_1)
#define GC9503V_LCD_IO_SPI_SCL_1 (IO_EXPANDER_PIN_NUM_2)
#define GC9503V_LCD_IO_SPI_SDO_1 (IO_EXPANDER_PIN_NUM_3)