#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

// AtomMatrix+EchoBase Board configuration

#include <driver/gpio.h>

#define AUDIO_INPUT_SAMPLE_RATE  24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_NC
#define AUDIO_I2S_GPIO_WS GPIO_NUM_19
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_33
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_23
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_22

#define AUDIO_CODEC_I2C_SDA_PIN  GPIO_NUM_25
#define AUDIO_CODEC_I2C_SCL_PIN  GPIO_NUM_21
#define AUDIO_CODEC_ES8311_ADDR  ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_GPIO_PA GPIO_NUM_NC

#define BUILTIN_LED_GPIO        GPIO_NUM_27
#define BOOT_BUTTON_GPIO        GPIO_NUM_39
#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_NC
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_NC



#endif // _BOARD_CONFIG_H_
