#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

// AtomEchoS3R Board configuration

#include <driver/gpio.h>

#define AUDIO_INPUT_REFERENCE    true
#define AUDIO_INPUT_SAMPLE_RATE  24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_11
#define AUDIO_I2S_GPIO_WS   GPIO_NUM_3
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_17
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_4
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_48

#define AUDIO_CODEC_I2C_SDA_PIN  GPIO_NUM_45
#define AUDIO_CODEC_I2C_SCL_PIN  GPIO_NUM_0
#define AUDIO_CODEC_ES8311_ADDR  ES8311_CODEC_DEFAULT_ADDR
#define AUDIO_CODEC_GPIO_PA      GPIO_NUM_18

#define BUILTIN_LED_GPIO        GPIO_NUM_NC
#define USER_BUTTON_GPIO        GPIO_NUM_41
#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_NC
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_NC


#endif // _BOARD_CONFIG_H_
