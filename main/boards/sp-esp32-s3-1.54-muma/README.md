【产品简介】
[] ESP32 S3小木马开发板1.54寸LCD小智muma虾哥AI DeepSeek人工智能语音聊天机器人N16R8
【功能】
[] 可爱小木马，支持天气时钟， SD视频播放， AI智能对话所有固件源码开源，适合小孩编程学习，可开发更多功能。
AI小智支持语音唤醒。触摸版本额外支持触摸唤醒和打断
显示屏：1.54寸ST7789 240x240分辨率
产品链接：
https://spotpear.cn/shop/ESP32-S3-AI-1.54-inch-LCD-Display-TouchScreen-N16R8-muma-DeepSeek/sp-esp32-s3-1.54-muma-W-Bat.html

# 编译配置命令

**配置编译目标为 ESP32S3：**

```bash
idf.py set-target esp32s3
```

**打开 menuconfig：**

```bash
idf.py menuconfig
```

**选择板子：**

```
<PERSON><PERSON> Assistant -> Board Type -> Spotpear ESP32-S3-LCD-1.54-MUMA
```

**编译：**

```bash
idf.py build
```
